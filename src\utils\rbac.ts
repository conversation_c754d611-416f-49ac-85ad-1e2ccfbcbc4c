import { Database } from '@/integrations/supabase/types';

export type UserRole = Database['public']['Enums']['user_role'];

export interface User {
  id: string;
  role: UserRole;
}

export interface AccessControlConfig {
  requiredRoles?: UserRole[];
  allowSelfAccess?: boolean;
  targetUserId?: string;
  requireOwnership?: boolean;
}

export interface AccessResult {
  hasAccess: boolean;
  reason?: string;
  isOwner?: boolean;
}

/**
 * Core role hierarchy and permissions
 */
export const ROLE_HIERARCHY: Record<UserRole, number> = {
  admin: 3,
  program_officer: 2,
  field_staff: 1,
};

/**
 * Permission sets for each role
 */
export const ROLE_PERMISSIONS = {
  admin: [
    'manage_users',
    'manage_schools',
    'manage_books',
    'manage_distributions',
    'view_all_reports',
    'manage_tasks',
    'view_analytics',
    'manage_system_settings',
    'access_impact_data',
    'manage_staff',
  ],
  program_officer: [
    'manage_schools',
    'manage_books',
    'manage_distributions',
    'view_all_reports',
    'manage_tasks',
    'view_analytics',
    'limited_user_management',
    'access_impact_data',
  ],
  field_staff: [
    'view_own_data',
    'create_reports',
    'manage_own_tasks',
    'check_in_out',
    'view_assigned_schools',
  ],
} as const;

/**
 * Check if a user has a specific permission
 */
export function hasPermission(userRole: UserRole, permission: string): boolean {
  return ROLE_PERMISSIONS[userRole]?.includes(permission as any) || false;
}

/**
 * Check if a user role has sufficient level for an operation
 */
export function hasRoleLevel(userRole: UserRole, requiredLevel: UserRole): boolean {
  return ROLE_HIERARCHY[userRole] >= ROLE_HIERARCHY[requiredLevel];
}

/**
 * Core access control logic
 */
export function checkAccess(
  user: User | null,
  config: AccessControlConfig
): AccessResult {
  // No user means no access
  if (!user) {
    return { hasAccess: false, reason: 'User not authenticated' };
  }

  const {
    requiredRoles = [],
    allowSelfAccess = false,
    targetUserId,
    requireOwnership = false,
  } = config;

  // Check if user has required role
  const hasRequiredRole = requiredRoles.length === 0 || requiredRoles.includes(user.role);
  
  if (!hasRequiredRole) {
    return { 
      hasAccess: false, 
      reason: `Role '${user.role}' not in required roles: ${requiredRoles.join(', ')}` 
    };
  }

  // Check ownership if required
  const isOwner = targetUserId ? user.id === targetUserId : false;
  
  if (requireOwnership && !isOwner) {
    return { 
      hasAccess: false, 
      reason: 'User does not own this resource',
      isOwner: false 
    };
  }

  // Admin and program officers have elevated access
  const hasElevatedAccess = user.role === 'admin' || user.role === 'program_officer';
  
  // Field staff can only access their own data unless they have elevated access
  if (user.role === 'field_staff' && targetUserId && !isOwner && !hasElevatedAccess) {
    return { 
      hasAccess: false, 
      reason: 'Field staff can only access their own data',
      isOwner: false 
    };
  }

  // Self access check
  if (allowSelfAccess && targetUserId && !isOwner && !hasElevatedAccess) {
    return { 
      hasAccess: false, 
      reason: 'User can only access their own data',
      isOwner: false 
    };
  }

  return { hasAccess: true, isOwner };
}

/**
 * Predefined access configurations for common scenarios
 */
export const ACCESS_CONFIGS = {
  ADMIN_ONLY: {
    requiredRoles: ['admin'] as UserRole[],
  },
  ADMIN_PROGRAM_OFFICER: {
    requiredRoles: ['admin', 'program_officer'] as UserRole[],
  },
  ALL_ROLES: {
    requiredRoles: ['admin', 'program_officer', 'field_staff'] as UserRole[],
  },
  SELF_ACCESS: {
    requiredRoles: ['admin', 'program_officer', 'field_staff'] as UserRole[],
    allowSelfAccess: true,
  },
  FIELD_STAFF_OWN_DATA: {
    requiredRoles: ['admin', 'program_officer', 'field_staff'] as UserRole[],
    requireOwnership: false,
    allowSelfAccess: true,
  },
} as const;

/**
 * Navigation access control
 */
export function canAccessRoute(userRole: UserRole, route: string): boolean {
  const routePermissions: Record<string, UserRole[]> = {
    dashboard: ['admin', 'program_officer', 'field_staff'],
    'field-visits': ['admin', 'program_officer', 'field_staff'],
    tasks: ['admin', 'program_officer', 'field_staff'],
    schools: ['admin', 'program_officer', 'field_staff'],
    books: ['admin', 'program_officer'],
    impact: ['admin'],
    'staff-management': ['admin', 'program_officer'],
    settings: ['admin', 'program_officer'],
    reports: ['admin'],
  };

  return routePermissions[route]?.includes(userRole) || false;
}

/**
 * Feature access control
 */
export function canAccessFeature(userRole: UserRole, feature: string): boolean {
  const featurePermissions: Record<string, UserRole[]> = {
    'create-user': ['admin'],
    'manage-user': ['admin'],
    'reset-password': ['admin', 'program_officer'],
    'view-all-reports': ['admin', 'program_officer'],
    'manage-books': ['admin', 'program_officer'],
    'manage-distributions': ['admin', 'program_officer'],
    'view-analytics': ['admin', 'program_officer'],
    'check-in-out': ['admin', 'program_officer', 'field_staff'],
    'create-report': ['admin', 'program_officer', 'field_staff'],
    'manage-own-tasks': ['admin', 'program_officer', 'field_staff'],
    'assign-tasks': ['admin', 'program_officer'],
  };

  return featurePermissions[feature]?.includes(userRole) || false;
}
