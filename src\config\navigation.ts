import { 
  Home, 
  UserCheck, 
  CheckSquare, 
  School, 
  BookOpen, 
  Award, 
  Users, 
  Settings, 
  HelpCircle,
  FileText,
  BarChart3,
  type LucideIcon
} from 'lucide-react';
import { type UserRole } from '@/utils/rbac';

export interface NavigationItem {
  id: string;
  label: string;
  icon: LucideIcon;
  route: string;
  roles: UserRole[];
  description?: string;
  children?: NavigationItem[];
}

/**
 * Simplified navigation structure
 * Each item represents a distinct feature area with clear role-based access
 */
export const navigationConfig: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: Home,
    route: 'dashboard',
    roles: ['admin', 'program_officer', 'field_staff'],
    description: 'Overview and key metrics',
  },
  {
    id: 'field-visits',
    label: 'Field Visits',
    icon: UserCheck,
    route: 'field-visits',
    roles: ['admin', 'program_officer', 'field_staff'],
    description: 'Check-in, reports, and field activities',
  },
  {
    id: 'tasks',
    label: 'Tasks',
    icon: CheckSquare,
    route: 'tasks',
    roles: ['admin', 'program_officer', 'field_staff'],
    description: 'Task management and assignments',
  },
  {
    id: 'schools',
    label: 'Schools',
    icon: School,
    route: 'schools',
    roles: ['admin', 'program_officer', 'field_staff'],
    description: 'School management and information',
  },
  {
    id: 'books',
    label: 'Books',
    icon: BookOpen,
    route: 'books',
    roles: ['admin', 'program_officer'],
    description: 'Book management and distributions',
  },
  {
    id: 'impact',
    label: 'Impact',
    icon: Award,
    route: 'impact',
    roles: ['admin'],
    description: 'Impact measurement and analytics',
    children: [
      {
        id: 'impact-overview',
        label: 'Overview',
        icon: BarChart3,
        route: 'impact',
        roles: ['admin'],
      },
      {
        id: 'impact-students',
        label: 'Student Outcomes',
        icon: Users,
        route: 'impact-students',
        roles: ['admin'],
      },
      {
        id: 'impact-feedback',
        label: 'Beneficiary Feedback',
        icon: FileText,
        route: 'impact-feedback',
        roles: ['admin'],
      },
      {
        id: 'impact-reports',
        label: 'Reports',
        icon: FileText,
        route: 'impact-reports',
        roles: ['admin'],
      },
    ],
  },
  {
    id: 'staff-management',
    label: 'Staff Management',
    icon: Users,
    route: 'staff-management',
    roles: ['admin', 'program_officer'],
    description: 'User management and permissions',
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: Settings,
    route: 'settings',
    roles: ['admin', 'program_officer'],
    description: 'System preferences and configuration',
  },
  {
    id: 'help',
    label: 'Help & Support',
    icon: HelpCircle,
    route: 'help',
    roles: ['admin', 'program_officer', 'field_staff'],
    description: 'Documentation and support',
    children: [
      {
        id: 'help-docs',
        label: 'Documentation',
        icon: FileText,
        route: 'help-docs',
        roles: ['admin'],
      },
    ],
  },
];

/**
 * Route mapping for simplified navigation
 * Maps routes to their corresponding components
 */
export const routeMapping: Record<string, string> = {
  // Core routes
  'dashboard': 'Dashboard',
  'field-visits': 'UnifiedFieldVisits',
  'tasks': 'UnifiedTaskManagement',
  'schools': 'SchoolList',
  'books': 'Books',
  'staff-management': 'StaffManagement',
  'settings': 'Settings',
  'help': 'Help',
  
  // Impact routes
  'impact': 'ImpactOverview',
  'impact-students': 'StudentOutcomes',
  'impact-feedback': 'BeneficiaryFeedback',
  'impact-reports': 'ImpactReports',
  
  // Help routes
  'help-docs': 'Documentation',
};

/**
 * Legacy route redirects
 * Maps old routes to new simplified routes
 */
export const legacyRouteRedirects: Record<string, string> = {
  // Attendance redirects to field-visits
  'attendance': 'field-visits',
  'attendance-sessions': 'field-visits',
  'session-management': 'field-visits',
  'attendance-gps': 'field-visits',
  'field-staff-checkin': 'field-visits',
  'field-staff-checkout': 'field-visits',
  
  // Task management consolidation
  'tasks-list': 'tasks',
  'tasks-assigned': 'tasks',
  'tasks-managed': 'tasks',
  'tasks-completed': 'tasks',
  'tasks-overdue': 'tasks',
  
  // School management consolidation
  'schools-list': 'schools',
  'schools-primary': 'schools',
  'schools-secondary': 'schools',
  'schools-inactive': 'schools',
  'schools-locations': 'schools',
  
  // Book management consolidation
  'book-management': 'books',
  'book-distributions': 'books',
  'distributions': 'books',
  'distributions-active': 'books',
  'distributions-history': 'books',
  
  // Reports consolidation under impact
  'reports': 'impact-reports',
  'attendance-reports': 'impact-reports',
  'field-staff-reports': 'impact-reports',
  'field-staff-analytics': 'impact-reports',
  
  // Settings consolidation
  'settings-profile': 'settings',
  'settings-notifications': 'settings',
};

/**
 * Get navigation items for a specific user role
 */
export function getNavigationForRole(userRole: UserRole): NavigationItem[] {
  return navigationConfig.filter(item => 
    item.roles.includes(userRole)
  ).map(item => ({
    ...item,
    children: item.children?.filter(child => child.roles.includes(userRole))
  }));
}

/**
 * Check if a route is accessible for a user role
 */
export function canAccessRoute(userRole: UserRole, route: string): boolean {
  // Check direct routes
  const directAccess = navigationConfig.some(item => 
    item.route === route && item.roles.includes(userRole)
  );
  
  if (directAccess) return true;
  
  // Check child routes
  return navigationConfig.some(item => 
    item.children?.some(child => 
      child.route === route && child.roles.includes(userRole)
    )
  );
}

/**
 * Get the component name for a route
 */
export function getComponentForRoute(route: string): string | null {
  // Check for legacy redirects first
  const redirectedRoute = legacyRouteRedirects[route] || route;
  return routeMapping[redirectedRoute] || null;
}

/**
 * Get breadcrumb path for a route
 */
export function getBreadcrumbPath(route: string): NavigationItem[] {
  const path: NavigationItem[] = [];
  
  for (const item of navigationConfig) {
    if (item.route === route) {
      path.push(item);
      break;
    }
    
    if (item.children) {
      const child = item.children.find(child => child.route === route);
      if (child) {
        path.push(item, child);
        break;
      }
    }
  }
  
  return path;
}
